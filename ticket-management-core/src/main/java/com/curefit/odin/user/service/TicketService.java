package com.curefit.odin.user.service;

import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.commons.sf.util.HeadersUtils;
import com.curefit.commons.sf.util.InvalidSeachQueryException;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.odin.admin.models.Category;
import com.curefit.odin.admin.models.Tenant;
import com.curefit.odin.admin.pojo.*;
import com.curefit.odin.admin.service.*;
import com.curefit.odin.enums.*;
import com.curefit.odin.notification.NotificationHandler;
import com.curefit.odin.user.models.FieldData;
import com.curefit.odin.user.models.Ticket;
import com.curefit.odin.user.pojo.*;
import com.curefit.odin.user.repositories.TicketDAO;
import com.curefit.odin.utils.*;
import com.curefit.odin.utils.pojo.ExternalTicketResponse;
import com.curefit.odin.utils.service.EmailDLService;
import com.curefit.odin.utils.service.LocationService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.io.IOException;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.odin.commons.Constants.*;
import static org.apache.commons.lang.StringUtils.isEmpty;

@Service
@Slf4j
public class TicketService extends BaseMySQLService<Ticket, TicketEntry> implements TicketCacheService<TicketEntry> {

    private static final Priority DEFAULT_PRIORITY = Priority.NA;

    private static final List<Status> DONE_STATUSES = Collections.singletonList(Status.RESOLVED);
    private static final List<Status> OPEN_STATUSES = Arrays.asList(Status.OPEN, Status.IN_PROGRESS);
    @Autowired
    TicketWatcherService ticketWatcherService;
    @Autowired
    LocationService locationService;
    @Autowired
    AssigneeQueueService assigneeQueueService;
    @Autowired
    RollbarService rollbarService;
    @Autowired
    QueueLocationAssigneesMappingService queueLocationAssigneesMappingService;
    @Autowired
    TicketAssigneeQueueService ticketAssigneeQueueService;
    @Autowired
    EmailDLService emailDLService;
    @Autowired
    CustomFieldService customFieldService;
    @Autowired
    AuthService authService;
    @Autowired
    ExternalTicketServices externalTicketServices;
    @Autowired
    SLAService slaService;
    @Autowired
    TicketEscalationService ticketEscalationService;
    @Autowired
    WatcherConfigService watcherConfigService;
    @Autowired
    TicketApprovalConfigService ticketApprovalConfigService;
    @Autowired
    CommentService commentService;
    @Autowired
    AttachmentService attachmentService;
    @Autowired
    NotificationHandler notificationHandler;
    @Autowired
    DefaultAssigneeService defaultAssigneeService;
    @Autowired
    IssueTemplateService issueTemplateService;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private CategoryService categoryService;
    @Autowired
    private SubCategoryService subCategoryService;
    @Autowired
    private FieldDataService fieldDataService;
    @Autowired
    private UserService userService;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private AppConfigCache appConfigCache;

    public TicketService(TicketDAO ticketDAO) {
        super(ticketDAO);
    }

    @Override
    public TicketEntry convertToEntry(Ticket ticket) {

        TicketEntry ticketEntry = super.convertToEntry(ticket);

        if (null != ticket.getCategory()) {
            ticketEntry.setCategoryName(ticket.getCategory().getName());
            ticketEntry.setCategoryId(ticket.getCategory().getId());
        }

        ticketEntry.setConfidential(isTicketConfidential(ticket));

        if (null != ticket.getSubCategory()) {
            ticketEntry.setSubCategoryName(ticket.getSubCategory().getName());
            ticketEntry.setSubCategoryId(ticket.getSubCategory().getId());
        }
        try {
            ticketEntry.setReporter(userService.findUserByMailId(ticket.getCreatedBy()));
        } catch (BaseException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }

        if (ticket.getParentTicket() != null) {
            ticketEntry.setParentTicketId(ticket.getParentTicket().getId());
        }

        ticketEntry.setTenantId(ticket.getTenant().getId());
        ticketEntry.setTenantName(ticket.getTenant().getName());

        if (ticket.getAssignee() != null) {
            try {
                ticketEntry.setAssignedUser(userService.findUserByMailId(ticket.getAssignee()));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        if (ticket.getLocation() != null) {
            try {
                ticketEntry.setLocationEntry(locationService.convertToEntry(ticket.getLocation()));
            } catch (NumberFormatException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        if (ticket.getMentions() != null) {
            ticketEntry.setUserMentions(ticket.getMentions().stream()
                    .map(userId -> userService.findUserByMailIdWithDefault(userId))
                    .collect(Collectors.toList()));
        }

        if (ticket.getAssignees() != null) {
            ticketEntry.setAssigneeQueueUsers(ticket.getAssignees().stream()
                    .map(assigneeId -> userService.findUserByMailIdWithDefault(assigneeId))
                    .collect(Collectors.toList()));
        }

        if (ticket.getAssigneeQueue() != null) {
            ticketEntry.setAssignedQueueId(ticket.getAssigneeQueue().getId());
            ticketEntry.setAssignedQueueName(ticket.getAssigneeQueue().getName());
        }

        ticketEntry.setFields(null);
        removeDeletedAttachment(ticketEntry);
        return ticketEntry;
    }

    private void removeDeletedAttachment(TicketEntry ticketEntry) {
        if (CollectionUtils.isNotEmpty(ticketEntry.getAttachments())) {
            ticketEntry.setAttachments(ticketEntry.getAttachments().stream()
                    .filter(BaseOdinEntry::getActive).collect(Collectors.toList()));
        }
    }

    @Override
    public Ticket convertToEntity(TicketEntry ticketEntry) {
        Ticket convertedEntity = super.convertToEntity(ticketEntry);

        if (ticketEntry.getTenantId() != null) {
            Tenant tenant;
            try {
                tenant = tenantService.fetchEntityById(ticketEntry.getTenantId());
                convertedEntity.setTenant(tenant);
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        if (ticketEntry.getCategoryId() != null) {
            Category category;
            try {
                category = categoryService.fetchEntityById(ticketEntry.getCategoryId());
                convertedEntity.setCategory(category);
                convertedEntity.setTenant(category.getTenant());
                if (ticketEntry.getSubCategoryId() != null) {
                    convertedEntity.setSubCategory(subCategoryService.fetchEntityById(ticketEntry.getSubCategoryId()));
                }
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }
        if (ticketEntry.getParentTicketId() != null) {
            try {
                convertedEntity.setParentTicket(fetchEntityById(ticketEntry.getParentTicketId()));
            } catch (BaseException e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        }

        if (ticketEntry.getFields() != null) {
            List<FieldData> fieldDataList = new ArrayList<>();
            for (FieldDataEntry fieldDataEntry : ticketEntry.getFields()) {
                fieldDataEntry.setCategoryId(ticketEntry.getCategoryId());
                FieldData fieldData = fieldDataService.convertToEntity(fieldDataEntry);
                fieldDataList.add(fieldData);
            }
            convertedEntity.setFields(fieldDataList);
            convertedEntity.setFieldJson(fieldDataService.getJSONForFields(ticketEntry.getFields()));
        }
        if (ticketEntry.getLocationEntry() != null && ticketEntry.getLocationEntry().getId() != null) {
            try {
                convertedEntity.setLocation(locationService.fetchEntityById(ticketEntry.getLocationEntry().getId()));
            } catch (BaseException e) {
                throw new RuntimeException(e);
            }
        }

        if (StringUtils.isNotBlank(ticketEntry.getDescription())) {
            List<String> userIds = UserIdParser.getUserIds(ticketEntry.getDescription());
            convertedEntity.setMentions(userIds);
        }

        if (ticketEntry.getAssigneeQueueUsers() == null) {
            if (ticketEntry.getAssignedUser() != null) {
                ticketEntry.setAssigneeQueueUsers(Collections.singletonList(ticketEntry.getAssignedUser()));
            } else if (ticketEntry.getAssignedQueueId() != null) {
                try {
                    convertedEntity.setAssigneeQueue(assigneeQueueService.fetchEntityById(ticketEntry.getAssignedQueueId()));
                    Long locationId = ticketEntry.getLocationEntry() != null ? ticketEntry.getLocationEntry().getId() : null;
                    Long assignedQueueId = ticketEntry.getAssignedQueueId();
                    ticketEntry.setAssigneeQueueUsers((getAutomatedAssignees(locationId, assignedQueueId, ticketEntry.getCreatedBy())));
                } catch (BaseException e) {
                    log.error(e.getMessage(), e);
                    rollbarService.error(e);
                }
            }
        }
        if (ticketEntry.getAssigneeQueueUsers() != null) {
            Set<String> assignees = getAssignees(ticketEntry);
            if (assignees.size() == 1) {
                convertedEntity.setAssignee(assignees.iterator().next());
            } else {
                convertedEntity.setAssignee(null);
            }
            convertedEntity.setAssignees(assignees);
        }

        return convertedEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    public TicketEntry createWithoutCustomFields(TicketEntry entry) throws BaseException {
        return createTicket(entry);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TicketEntry create(TicketEntry entry) throws BaseException {
        entry.setDescription(OdinStringUtils.replaceIncompatibleCharactersWithSpace(entry.getDescription()));
        this.validate(entry);
        fieldDataService.validate(entry);
        return createTicket(entry);
    }

    // Prevent Asset level duplication of tickets
    private void validate(TicketEntry entry) throws BaseException {
        if (entry.getIssueTemplateId() != null && entry.getAssetReferenceId() != null && entry.getLocationEntry().getId() != null) {
            List<Ticket> activeTickets = ((TicketDAO) baseMySQLRepository).findAllByAssetReferenceIdAndIssueTemplateIdAndLocationAndStatusNotIn(entry.getAssetReferenceId(), entry.getIssueTemplateId(), entry.getLocationEntry().getId(), Status.closedTicketStatus());
            if (!activeTickets.isEmpty()) {
                String errorMsg = String.format("Ticket already exists for the selected location: %s and the asset: %s", entry.getLocationEntry().getCenterName(), entry.getAssetReferenceId());
                throw new BaseException(errorMsg, LogType.ERROR, "ODIN", AppStatus.RESOURCE_EXISTS, "odin", errorMsg);
            }
        }
    }

    public List<TicketEntry> findTicketsByCreatedBy(String createdBy) {
        return ((TicketDAO) baseMySQLRepository).findAllByCreatedBy(createdBy).stream()
                .map(this::convertToEntry)
                .collect(Collectors.toList());
    }

    private TicketEntry createTicket(TicketEntry entry) throws BaseException {
        entry.setStatus(Status.OPEN);
        if (entry.getPriority() == null) {
            entry.setPriority(DEFAULT_PRIORITY);
        }

        List<AttachmentEntry> attachmentEntriesToBeCreated = entry.getAttachments();
        entry.setAttachments(null);

        TicketEntry ticketEntry = super.create(entry);

        Long id = ticketEntry.getId();
        if (CollectionUtils.isNotEmpty(attachmentEntriesToBeCreated)) {
            List<AttachmentEntry> attachmentEntries = attachmentService.addAttachmentsToTicket(id, attachmentEntriesToBeCreated);
            ticketEntry.setAttachments(attachmentEntries);
        }

        Set<String> assigneeIds = null;
        if (CollectionUtils.isNotEmpty(ticketEntry.getAssigneeQueueUsers())) {
            assigneeIds = ticketEntry.getAssigneeQueueUsers().stream()
                    .map(UserEntry::getEmailId)
                    .collect(Collectors.toSet());
            ticketAssigneeQueueService.addAssigneesToTicket(id, assigneeIds);
        }

        ticketEscalationService.scheduleEscalationRules(ticketEntry);
        addWatchers(ticketEntry, assigneeIds);

        ticketEntry.setFields(fieldDataService.findFieldDataByTicketId(id));
        fieldDataService.evictFindFieldDataByTicketIdCache(id);
        String namespace = HeadersUtils.getNamespace();
        createOrUpdateTicketInExternalServices(namespace, ticketEntry);
        return ticketEntry;
    }

    private void addWatchers(TicketEntry ticketEntry, Set<String> assigneeIds) {
        try {
            Set<String> watchers = new HashSet<>();
            watchers.addAll(ticketEscalationService.fetchEscalationUsers(ticketEntry));
            watchers.addAll(watcherConfigService.fetchWatchers(ticketEntry));
            watchers.addAll(ticketApprovalConfigService.fetchTicketApprovers(ticketEntry));
            if (CollectionUtils.isNotEmpty(assigneeIds)) {
                watchers.addAll(assigneeIds);
            }
            ticketWatcherService.createTicketWatchers(ticketEntry.getId(), watchers);
        } catch (Exception e) {
            String errorMsg = String.format("Exception in adding watchers to ticket: %s", ticketEntry.getId());
            log.error(errorMsg, e);
            rollbarService.error(e, errorMsg);
        }
    }

    private void createOrUpdateTicketInExternalServices(String namespace, TicketEntry ticketEntry) {
        AsyncService.submit(() -> {
            try {
                externalTicketServices.updateTicket(ticketEntry);
                ExternalTicketResponse response = externalTicketServices.createTicket(namespace, ticketEntry);
                if (response != null) {
                    TicketEntry updatedEntry = new TicketEntry();
                    updatedEntry.setDest(response.getDest());
                    updatedEntry.setDestRefId(response.getId());
                    patchUpdate(ticketEntry.getId(), updatedEntry);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TicketEntry patchUpdate(Long id, TicketEntry entry) throws BaseException {
        authService.checkAuthorized(id, this);
        updateDueDate(id, entry);
        entry.setId(id);
        updateAssignedUsers(id, entry);
        TicketEntry updatedEntry = super.patchUpdate(id, entry);
        fieldDataService.evictFindFieldDataByTicketIdCache(id);
        return updatedEntry;
    }

    public void updateAssignedUsers(Long id, TicketEntry entry) throws BaseException {
        TicketEntry dbTicketEntry = findOneById(id);
        Set<String> assigneeIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(entry.getAssigneeQueueUsers())) {
            assigneeIds.addAll(entry.getAssigneeQueueUsers().stream().map(UserEntry::getEmailId).collect(Collectors.toSet()));
        }

        Long locationId = dbTicketEntry.getLocationEntry() != null ? dbTicketEntry.getLocationEntry().getId() : null;
        DefaultAssigneeEntry assignees = defaultAssigneeService.fetchDefaultAssignee(locationId, entry.getCategoryId(), entry.getSubCategoryId(), null);
        if (assignees != null) {
            entry.setAssignedQueueId(assignees.getAssigneeQueueEntry().getId());
            if (CollectionUtils.isNotEmpty(assignees.getAssignedUsers())) {
                assigneeIds.addAll(assignees.getAssignedUsers().stream().map(UserEntry::getEmailId).collect(Collectors.toSet()));
            }
        }

        if (CollectionUtils.isNotEmpty(assigneeIds)) {
            entry.setAssigneeQueueUsers(assigneeIds.stream()
                    .map(assigneeId -> userService.findUserByMailIdWithDefault(assigneeId))
                    .filter(userEntry -> !DISABLED_USER.equalsIgnoreCase(userEntry.getName()))
                    .collect(Collectors.toList()));
            ticketAssigneeQueueService.updateAssigneesToTicket(id, assigneeIds);
        }

    }

    private void updateDueDate(Long id, TicketEntry entry) throws BaseException {
        if (entry.getPriority() != null) {
            TicketEntry ticketEntry = findOneById(id);
            if (ticketEntry.getPriority() != entry.getPriority()) {
                entry.setDueDate(new Date(slaService.getDueDate(ticketEntry.getCreatedOn().getTime(), ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId(), entry.getPriority())));
                ticketEscalationService.scheduleEscalationRules(ticketEntry, entry.getPriority(), entry.getDueDate());
                updatePriorityInDestination(ticketEntry, entry.getPriority());
            }
        }
    }

    private Set<String> getAssignees(TicketEntry entry) {
        return entry.getAssigneeQueueUsers().stream()
                .map(UserEntry::getEmailId)
                .filter(emailId -> !StringUtils.isBlank(emailId))
                .filter(emailId -> !DISABLED_USER.equalsIgnoreCase(userService.findUserByMailIdWithDefault(emailId).getName()))
                .collect(Collectors.toSet());
    }

    private List<UserEntry> getAutomatedAssignees(Long locationId, Long assigneeQueueId, String email) {
        Set<String> assignees = queueLocationAssigneesMappingService.fetchByLocationAndQueueId(locationId, assigneeQueueId, email, false);
        return assignees.stream()
                .filter(assignee -> !StringUtils.isBlank(assignee))
                .map(assignee -> userService.findUserByMailIdWithDefault(assignee))
                .filter(userEntry -> !DISABLED_USER.equalsIgnoreCase(userEntry.getName()))
                .collect(Collectors.toList());
    }

    public List<Status> findNextStatus(Long id) throws BaseException, IOException {
        List<Long> rejectedBlacklistCategories = appConfigCache.getConfig("REJECTED_BLACKLISTED_CATEGORIES", new TypeReference<>() {
        }, new ArrayList<>());
        log.info("Finding next status for id {}", id);
        Ticket ticket = fetchEntityById(id);
        Integer maxStatusChangeTime = ticket.getCategory() != null ? ticket.getCategory().getMaxStatusChangeTime() : null;
        if (maxStatusChangeTime != null && ticket.getClosedAt() != null) {
            if (System.currentTimeMillis() - ticket.getClosedAt().getTime() > maxStatusChangeTime * MILLI_SEC_IN_HOUR) {
                return Collections.singletonList(ticket.getStatus());
            }
        }
        String currentUser = HeadersUtils.getCurrentUser();
        Set<String> ticketApprovers = ticketApprovalConfigService.fetchTicketApprovers(convertToEntry(ticket));
        List<Status> statuses;
        if (CollectionUtils.isNotEmpty(ticketApprovers)) {
            if (ticketApprovers.contains(currentUser)) {
                statuses = Status.approversTicketStatus();
            } else {
                statuses = Status.approvalRequiredTicketStatus();
            }
        } else {
            statuses = Status.fetchAll();
        }
        boolean isRejectedBlacklistedCategory = ticket.getCategory() != null && rejectedBlacklistCategories.contains(ticket.getCategory().getId());
        return filteredNextStatus(statuses, isRejectedBlacklistedCategory);
    }

    private List<Status> filteredNextStatus(List<Status> statuses, boolean isRejectedBlacklistedCategory) {
        if (isRejectedBlacklistedCategory) {
            return statuses.stream()
                    .filter(status -> status != Status.REJECTED && status != Status.ON_HOLD)
                    .collect(Collectors.toList());
        }
        return statuses;
    }

    public TicketEntry deleteLabelByTicketId(Long id, String label) throws BaseException {
        log.info("Deleting label {} from ticket {}", label, id);
        TicketEntry entry = findOneById(id);
        Set<String> labels = null == entry.getLabels() ? new HashSet<>() : entry.getLabels();
        labels.remove(label);
        TicketEntry updatedEntry = new TicketEntry();
        updatedEntry.setLabels(labels);
        return patchUpdate(id, updatedEntry);
    }

    public TicketEntry addLabelByTicketId(Long id, String label) throws BaseException {
        log.info("Adding label {} from ticket {}", label, id);
        TicketEntry entry = findOneById(id);
        Set<String> labels = null == entry.getLabels() ? new HashSet<>() : entry.getLabels();
        labels.add(label);
        TicketEntry updatedEntry = new TicketEntry();
        updatedEntry.setLabels(labels);
        return patchUpdate(id, updatedEntry);
    }

    @Transactional(rollbackFor = Exception.class)
    public void bulkUpdateStatus(BulkUpdateTicketStatusRequest request) throws BaseException {
        for (long id : request.getTicketIds()) {
            updateStatus(id, Status.get(request.getNewStatus()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public TicketEntry updateStatus(Long id, Status status) throws BaseException {
        log.info("update status of ticketId: {} to {}", id, status);

        TicketEntry currentTicketEntry = findOneById(id);
        populateDetails(currentTicketEntry);

        if (DONE_STATUSES.contains(status)) {
            fieldDataService.validateOnClosing(currentTicketEntry);
        }

        Status prevStatus = currentTicketEntry.getStatus();
        log.info("changing ticket status from {} to {} for ticketId: {}", prevStatus, status, id);

        if (prevStatus == status) {
            return currentTicketEntry;
        }

        TicketEntry ticketEntry = new TicketEntry();
        ticketEntry.setStatus(status);

        Date currentTime = new Date();
        if (DONE_STATUSES.contains(prevStatus) && OPEN_STATUSES.contains(status) && currentTicketEntry.getReOpenedAt() == null) {
            ticketEntry.setReOpenedAt(currentTime);
        }

        if (DONE_STATUSES.contains(status)) {
            ticketEntry.setClosedAt(currentTime);
        }

        ticketEntry = patchUpdate(id, ticketEntry);

        List<String> currentAssignees = null;
        if (currentTicketEntry.getAssigneeQueueUsers() != null) {
            currentAssignees = currentTicketEntry.getAssigneeQueueUsers().stream().map(UserEntry::getEmailId).toList();
        }

        String userId = HeadersUtils.getCurrentUser();
        String namespace = HeadersUtils.getNamespace();

        if (currentAssignees != null && currentAssignees.size() != 1 && currentAssignees.contains(userId)) {
            ticketEntry = updateTicketAssignees(id, Collections.singletonList(userId));
        }

        if (!DONE_STATUSES.contains(status) && ticketEntry.getClosedAt() != null) {
            log.info("updating closedAt to null for ticket id {}", id);
            int updated = ((TicketDAO) baseMySQLRepository).updateTicketClosedAt(id, null, currentTime);
            if (updated == 1) {
                log.info("ticket updated with closedAt as null for ticket id {}", id);
                ticketEntry.setClosedAt(null);
            } else {
                rollbarService.error(new Exception("unable to reset closedAt to null for ticket " + id));
            }
        }

        populateDetails(ticketEntry);

        updateExternalTickets(userId, namespace, ticketEntry, prevStatus);
        return ticketEntry;
    }

    private void updateExternalTickets(String userId, String namespace, TicketEntry ticketEntry, Status prevStatus) {
        AsyncService.submit(() -> {
            try {
                if (TICKET_COMPLETE_STATUSES.contains(ticketEntry.getStatus()) || TICKET_COMPLETE_STATUSES.contains(prevStatus)) {
                    externalTicketServices.updateStatus(ticketEntry);
                    externalTicketServices.updateStatusForDest(ticketEntry);
                }
                if (ticketEntry.getStatus() == Status.OPEN) {
                    externalTicketServices.updateStatusForDest(ticketEntry);
                }
                externalTicketServices.addMessage(namespace, ticketEntry, userId + " updated ticket status from - " + prevStatus.getName() + " to " + ticketEntry.getStatus().getName());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        });
    }

    public void sendLSAAboutToBreachReminderForTicketsHavingDueDateAsTomorrow() throws BaseException {
        List<Status> statusList = new ArrayList<>();
        statusList.add(Status.RESOLVED);
        statusList.add(Status.REJECTED);

        long tomorrowEpoch = new DateTime().plusDays(1).toDate().getTime();

        Date dueDateStartInIST = DateUtils.getStartOfDayDateInTimezone(tomorrowEpoch, "IST");
        Date dueDateEndInIST = DateUtils.getEndOfDayDateInTimezone(tomorrowEpoch, "IST");
        Date dueDateStartInUTC = new Date(dueDateStartInIST.getTime());
        Date dueDateEndInUTC = new Date(dueDateEndInIST.getTime());

        List<Ticket> tickets = ((TicketDAO) baseMySQLRepository).getByStatusNotInAndDueDateBetweenAndActive(
                statusList,
                dueDateStartInUTC,
                dueDateEndInUTC,
                true);
        for (Ticket ticket : tickets) {
            SLAAboutToBreachIn24Hours(ticket.getId());
        }
    }

    public void SLAAboutToBreachIn24Hours(Long ticketId) throws BaseException {
        TicketEntry ticketEntry = fetchDetail(ticketId);
        if (ticketEntry.getDueDate() == null) {
            log.info("ticket due date is null, ignoring the event for ticketId {} ", ticketId);
            return;
        }

        if (TICKET_DONE_STATUSES.contains(ticketEntry.getStatus())) {
            log.info("ticket status is closed, ignoring the event for ticketId {}", ticketId);
            return;
        }
        notificationHandler.handleTicketSLAReminderToWatchers(ticketEntry);
    }

    @Transactional(rollbackFor = Exception.class)
    public TicketEntry updateAssignees(Long id, List<String> userIds) throws BaseException {
        TicketEntry ticketEntry = updateTicketAssignees(id, userIds);
        populateDetails(ticketEntry);
        return ticketEntry;
    }

    private TicketEntry updateTicketAssignees(Long id, List<String> userIds) throws BaseException {
        log.info("update assignees of ticketId: {} to {}", id, userIds);
        TicketEntry oldTicketEntry = findOneById(id);
        TicketEntry ticketEntry = new TicketEntry();
        ticketEntry.setAssigneeQueueUsers(userIds.stream().map(UserEntry::new).collect(Collectors.toList()));
        ticketEntry = patchUpdate(id, ticketEntry);

        ticketAssigneeQueueService.updateAssigneesToTicket(ticketEntry.getId(), ticketEntry.getAssigneeQueueUsers().stream()
                .map(UserEntry::getEmailId)
                .collect(Collectors.toSet()));

        updateAssigneesInDestination(oldTicketEntry, ticketEntry, userIds);
        return ticketEntry;
    }

    private void updateAssigneesInDestination(TicketEntry oldTicketEntry, TicketEntry ticketEntry, List<String> newAssignees) {
        List<UserEntry> assigneeQueueUsers = oldTicketEntry.getAssigneeQueueUsers() == null ? Collections.emptyList() : oldTicketEntry.getAssigneeQueueUsers();
        List<String> oldAssignees = assigneeQueueUsers.stream().map(UserEntry::getEmailId).toList();

        String user = HeadersUtils.getCurrentUser();
        String namespace = HeadersUtils.getNamespace();
        AsyncService.submit(() -> {
            try {
                externalTicketServices.updateAssignees(ticketEntry);
                externalTicketServices.addMessage(namespace, ticketEntry, user + " updated Assignees from - " + oldAssignees + " to " + newAssignees.toString());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                rollbarService.error(e);
            }
        });
    }

    private void updatePriorityInDestination(TicketEntry ticketEntry, Priority priority) {
        if (ticketEntry.getPriority() != priority) {
            String user = HeadersUtils.getCurrentUser();
            String namespace = HeadersUtils.getNamespace();
            AsyncService.submit(() -> {
                try {
                    externalTicketServices.addMessage(namespace, ticketEntry, user + " updated Priority from - " + (ticketEntry.getPriority() != null ? ticketEntry.getPriority().getName() : "") + " to " + priority.getName());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    rollbarService.error(e);
                }
            });
        }
    }

    public List<Long> jsonCustomFieldFilter(Map<String, List<String>> fields) {
        String queryMain = "";
        for (Map.Entry<String, List<String>> jsonField : fields.entrySet()) {
            if (!queryMain.isEmpty()) {
                queryMain = queryMain.concat(" AND ");
            }
            queryMain = queryMain.concat(" (");
            String query = "";
            for (String val : jsonField.getValue()) {
                if (!query.isEmpty()) {
                    query = query.concat(" OR ");
                }
                query = query.concat("JSON_SEARCH(t.fieldJson, 'all', '" + val + "',NULL,'$.\""
                        + jsonField.getKey() + "\"') IS NOT NULL");
            }
            queryMain = queryMain + query + ")";
        }
        List<BigInteger> list = (List<BigInteger>) entityManager.createNativeQuery("SELECT t.id FROM ticket t WHERE " + queryMain)
                .getResultList();

        return list.stream().map(BigInteger::longValue)
                .collect(Collectors.toList());
    }

    public TicketEntry fetchDetail(Long id) throws BaseException {
        TicketEntry ticketEntry = findOneById(id);
        populateDetails(ticketEntry);
        return ticketEntry;
    }

    public void populateDetails(TicketEntry ticketEntry) {
        ticketEntry.setIsAssignedToMe(isAssignedToMe(ticketEntry));
        List<FieldDataEntry> customFieldDataEntryList = fieldDataService.findFieldDataByTicketId(ticketEntry.getId());
        ticketEntry.setFields(customFieldDataEntryList);

        List<TicketWatcherEntry> watchers = ticketWatcherService.findByTicketId(ticketEntry.getId());
        ticketEntry.setWatchers(watchers.stream()
                .map(TicketWatcherEntry::getUser)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));

        CustomFieldFilterRequestEntry filter = new CustomFieldFilterRequestEntry();
        filter.setTenantId(ticketEntry.getTenantId());
        filter.setCategoryId(ticketEntry.getCategoryId());
        filter.setSubCategoryId(ticketEntry.getSubCategoryId());
        ticketEntry.setDefaultFields(customFieldService.filterActiveV2(filter).stream()
                .filter(field -> FieldType.DEFAULT.equals(field.getType()))
                .map(CustomFieldEntry::getName)
                .collect(Collectors.toList()));

        ticketEntry.setApprovers(ticketApprovalConfigService.fetchTicketApprovers(ticketEntry));
    }

    @Override
    public TicketEntry findOneById(Long id) throws BaseException {
        TicketEntry ticketEntry = super.findOneById(id);
        authService.checkAuthorized(ticketEntry, this);
        return ticketEntry;
    }

    @Cacheable(value = "findTicketById", unless = "#result == null")
    public TicketEntry findTicketById(Long id) throws BaseException {
        return super.findOneById(id);
    }

    public Long findTicketIdById(Long id) {
        return id;
    }

    @Override
    public Long findTicketId(TicketEntry entry) {
        return entry.getId();
    }

    private boolean isAssignedToMe(TicketEntry ticketEntry) {
        String userId = HeadersUtils.getCurrentUser();
        if (ticketEntry.getAssigneeQueueUsers() != null) {
            return ticketEntry.getAssigneeQueueUsers().stream()
                    .map(UserEntry::getEmailId)
                    .anyMatch(assigneeUserId -> assigneeUserId.equals(userId) || emailDLService.getEmailsByDL(assigneeUserId).contains(userId));
        }
        return false;
    }

    @Cacheable(value = "fetchTicketIdBySourceRef", unless = "#result == null")
    public Long fetchTicketIdBySourceRef(TicketSource ticketSource, String sourceRefId) {
        try {
            List<TicketEntry> tickets = search(0, -1, null, null, "source.eq:" + ticketSource + ";sourceRefId.eq:" + sourceRefId).getElements();
            if (!tickets.isEmpty()) {
                return tickets.getFirst().getId();
            }
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return null;
    }

    public Long fetchTicketIdByDestRef(TicketDest ticketDest, String destRefId) {
        try {
            List<TicketEntry> tickets = search(0, -1, null, null, "dest.eq:" + ticketDest + ";destRefId.eq:" + destRefId).getElements();
            if (!tickets.isEmpty()) {
                return tickets.getFirst().getId();
            }
        } catch (InvalidSeachQueryException e) {
            log.error(e.getMessage(), e);
            rollbarService.error(e);
        }
        return null;
    }

    private boolean isTicketConfidential(Ticket ticket) {
        if (ticket.getCategory() == null) {
            return false;
        }
        Boolean isConfidential = ticket.getCategory().getIsConfidential();
        if (isConfidential == null) {
            return false;
        }
        return isConfidential;
    }

    @Transactional(rollbackFor = Exception.class)
    public TicketEntry updateTicketCategory(TicketEntry request) throws BaseException {
        TicketEntry existingTicketEntry = findOneById(request.getId());

        TicketEntry existingTicketEntryCopy = new TicketEntry();
        copyNonNullProperties(existingTicketEntry, existingTicketEntryCopy);

        existingTicketEntry.setCategoryId(request.getCategoryId());
        existingTicketEntry.setSubCategoryId(request.getSubCategoryId());
        existingTicketEntry.setDueDate(request.getDueDate());
        existingTicketEntry.setAssignedQueueId(request.getAssignedQueueId());
        existingTicketEntry.setAssignedUser(request.getAssignedUser());
        existingTicketEntry.setAssigneeQueueUsers(request.getAssigneeQueueUsers());
        existingTicketEntry.setFields(request.getFields());
        existingTicketEntry.setPriority(request.getPriority());

        fieldDataService.validate(existingTicketEntry);
        TicketEntry updatedTicketEntry = patchUpdate(request.getId(), existingTicketEntry);
        fieldDataService.evictFindFieldDataByTicketIdCache(request.getId());
        populateDetails(updatedTicketEntry);
        fieldDataService.evictFindFieldDataByTicketIdCache(updatedTicketEntry.getId());
        addUpdateTicketCategoryComment(existingTicketEntryCopy, updatedTicketEntry);

        return updatedTicketEntry;
    }

    private void addUpdateTicketCategoryComment(TicketEntry existingTicketEntry, TicketEntry updatedTicketEntry) throws BaseException {
        CommentEntry entry = new CommentEntry();
        entry.setTicketId(updatedTicketEntry.getId());
        entry.setCreatedBy("system");
        String currentUser = HeadersUtils.getCurrentUser();
        String comment = String.format("[user:%s] has updated the ticket details.", currentUser);
        if (existingTicketEntry.getCategoryId() != updatedTicketEntry.getCategoryId() || existingTicketEntry.getSubCategoryId() != updatedTicketEntry.getSubCategoryId()) {
            comment += String.format("<br/>Category: <b><strike>[ %s ]</strike></b> -> <b>[ %s ]</b>", getCategoryName(existingTicketEntry), getCategoryName(updatedTicketEntry));
        }
        String oldAssignee = existingTicketEntry.getAssignedUser() != null ? existingTicketEntry.getAssignedUser().getEmailId() : null;
        String newAssignee = updatedTicketEntry.getAssignedUser() != null ? updatedTicketEntry.getAssignedUser().getEmailId() : null;
        if (!Objects.equals(oldAssignee, newAssignee)) {
            comment += String.format("<br/>Assignee: <strike> [user:%s] </strike> -> [user:%s] ", oldAssignee, newAssignee);
        }
        if (!Objects.equals(existingTicketEntry.getPriority(), updatedTicketEntry.getPriority())) {
            comment += String.format("<br/>Priority: <strike> %s </strike> -> %s ", existingTicketEntry.getPriority(), updatedTicketEntry.getPriority());
        }
        if (!Objects.equals(existingTicketEntry.getDueDate(), updatedTicketEntry.getDueDate())) {
            comment += String.format("<br/>Due Date: <strike> %s </strike> -> %s ", existingTicketEntry.getDueDate(), updatedTicketEntry.getDueDate());
        }
        if (!(updatedTicketEntry.getFields() == null || updatedTicketEntry.getFields().isEmpty())) {
            comment += String.format("<br/>New Custom Fields: %s", getFormattedCustomFields(updatedTicketEntry.getFields()));
        }
        entry.setComment(comment);
        commentService.create(entry);
    }

    private String getFormattedCustomFields(List<FieldDataEntry> fields) {
        StringBuilder fieldsString = new StringBuilder();
        for (FieldDataEntry field : fields) {
            if (field.getKey() == null || field.getValue() == null) continue;
            String value;
            if (field.getKey().getDataType() == DataType.LIST) {
                List<FieldValueEntry> fieldValues = (List<FieldValueEntry>) field.getValue();
                value = String.format("[%s]", fieldValues.stream().map(FieldValueEntry::getValue).collect(Collectors.joining(", ")));
            } else {
                value = field.getValue().toString();
            }
            fieldsString.append(String.format("<br/> - %s: %s", field.getKey().getValue(), value));
        }
        return fieldsString.toString();
    }

    private String getCategoryName(TicketEntry ticketEntry) {
        return StringUtils.isEmpty(ticketEntry.getSubCategoryName())
                ? ticketEntry.getCategoryName()
                : String.format("%s - %s", ticketEntry.getCategoryName(), ticketEntry.getSubCategoryName());
    }

    public List<TicketEntry> fetchOpenTicketsToSendSlaReminder() {
        long currentTime = System.currentTimeMillis();
        Date startTime = new Date(currentTime);
        Date endTime = new Date(currentTime + SLA_REMINDER_WINDOW_IN_MILLISEC);
        return ((TicketDAO) baseMySQLRepository).fetchOpenTicketsToSendSlaReminderDueBetween(startTime, endTime, Status.slaDisabledStatus())
                .stream().map(this::convertToEntry).collect(Collectors.toList());
    }

    public void markSLAReminderSent(Long id) throws BaseException {
        TicketEntry ticket = new TicketEntry();
        ticket.setSlaReminderSentAt(new Date());
        patchUpdate(id, ticket);
    }

    @Transactional(rollbackFor = Exception.class)
    public TicketEntry rateTicketResolution(Long id, Integer rating) throws BaseException {
        TicketEntry ticketEntry = findOneById(id);
        String currentUser = HeadersUtils.getCurrentUser();
        // Check if Ticket is resolved before rating
        if (!DONE_STATUSES.contains(ticketEntry.getStatus())) {
            throw new BaseException(String.format("Error in rating ticket resolution. Ticket-%s is not resolved.", id), AppStatus.BAD_REQUEST);
        }
        // Check if CurrentUser is the reporter
        if (ticketEntry.getReporter() == null || !currentUser.equals(ticketEntry.getReporter().getEmailId())) {
            throw new BaseException(String.format("Error in rating ticket resolution. User %s is not the reporter of Ticket-%s.", currentUser, id), AppStatus.BAD_REQUEST);
        }
        // Validate rating value
        if (rating == null || rating < 1 || rating > 4) {
            throw new BaseException(String.format("Error in rating ticket resolution. Invalid rating value. Should be between 1 - 4.", currentUser, id), AppStatus.BAD_REQUEST);
        }
        TicketEntry newEntry = new TicketEntry();
        newEntry.setResolutionRating(rating);
        TicketEntry updatedTicketEntry = patchUpdate(id, newEntry);
        populateDetails(updatedTicketEntry);
        return updatedTicketEntry;
    }

    public TicketEntry createV2(TicketEntry ticketEntry) throws BaseException {
        // fetch location entry from centerServiceRefId
        if (ticketEntry.getCenterServiceRefId() != null) {
            if (ticketEntry.getLocationEntry() == null) {
                ticketEntry.setLocationEntry(locationService.getByCenterServiceIdAndTenant(ticketEntry.getCenterServiceRefId(), ticketEntry.getTenantId()));
            }
        }
        // set priority
        if (ticketEntry.getIssueTemplateId() != null) {
            IssueTemplateEntry issueTemplate = issueTemplateService.findOneById(ticketEntry.getIssueTemplateId());
            if (ticketEntry.getPriority() == null) {
                ticketEntry.setPriority(issueTemplate.getPriority());
            }
            if (ticketEntry.getTitle() == null) {
                ticketEntry.setTitle(issueTemplate.getTitle());
            }
        }
        // set Description
        if (isEmpty(ticketEntry.getDescription())) {
            ticketEntry.setDescription("NA");
        }
        // set default priority
        if (ticketEntry.getPriority() == null) {
            ticketEntry.setPriority(Priority.P5);
        }

        // set due date
        ticketEntry.setDueDate(new Date(slaService.getDueDate(System.currentTimeMillis(), ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId(), ticketEntry.getPriority())));

        // set assignedQueueId
        DefaultAssigneeEntry defaultAssigneeEntry = defaultAssigneeService.fetchDefaultAssignee(ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId());
        if (defaultAssigneeEntry != null) {
            ticketEntry.setAssignedQueueId(defaultAssigneeEntry.getAssigneeQueueEntry().getId());
        }

        return this.create(ticketEntry);
    }

    public TicketEntry refreshTicket(Long ticketId, Boolean updateAssignee, Boolean updateDueDate) throws BaseException {
        TicketEntry ticketEntry = this.findOneById(ticketId);

        if (ticketEntry.getIssueTemplateId() != null) {
            IssueTemplateEntry issueTemplate = issueTemplateService.findOneById(ticketEntry.getIssueTemplateId());
            if (issueTemplate != null && issueTemplate.getPriority() != null) {
                ticketEntry.setPriority(issueTemplate.getPriority());
            }
        }

        if (updateDueDate) {
            ticketEntry.setDueDate(new Date(slaService.getDueDate(ticketEntry.getCreatedOn().getTime(), ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId(), ticketEntry.getPriority())));
            ticketEscalationService.scheduleEscalationRules(ticketEntry, ticketEntry.getPriority(), ticketEntry.getDueDate());
            updatePriorityInDestination(ticketEntry, ticketEntry.getPriority());
        }

        if (updateAssignee) {
            DefaultAssigneeEntry assignee = defaultAssigneeService.fetchDefaultAssignee(ticketEntry.getLocationEntry().getId(), ticketEntry.getCategoryId(), ticketEntry.getSubCategoryId(), null);
            if (assignee != null) {
                ticketAssigneeQueueService.updateAssigneesToTicket(ticketId, assignee.getAssignedUsers().stream().map(UserEntry::getEmailId).collect(Collectors.toSet()));
                ticketEntry.setAssignedQueueId(assignee.getAssigneeQueueEntry().getId());
                ticketEntry.setAssigneeQueueUsers(assignee.getAssignedUsers());
            }
        }

        return patchUpdate(ticketId, ticketEntry);
    }

    public List<TicketEntry> refreshTickets(List<Long> ticketIds, Boolean updateAssignee, Boolean updateDueDate) throws BaseException {
        List<TicketEntry> updatedTickets = new ArrayList<>();
        for (Long ticketId : ticketIds) {
            try {
                TicketEntry updatedTicket = refreshTicket(ticketId, updateAssignee, updateDueDate);
                updatedTickets.add(updatedTicket);
            } catch (Exception e) {
                log.error("Error while refreshing ticketId: {}", ticketId, e);
            }

        }
        return updatedTickets;
    }

    public List<TicketEntry> getOpenTickets(Long categoryId, Long subCategoryId, Long issueTemplateId, Date createdOn) throws InvalidSeachQueryException {
        String formattedDate = new SimpleDateFormat("yyyy-MM-dd").format(createdOn);
        StringBuilder queryBuilder = new StringBuilder();
        if (categoryId != null) {
            queryBuilder.append("category.id.eq:").append(categoryId).append(";");
        }
        if (subCategoryId != null) {
            queryBuilder.append("subCategory.id.eq:").append(subCategoryId).append(";");
        }
        if (issueTemplateId != null) {
            queryBuilder.append("issueTemplateId.eq:").append(issueTemplateId).append(";");
        }
        queryBuilder.append("status.nin:" + Status.closedTicketStatusV2().stream().map(Status::toString).collect(Collectors.joining(","))).append(";");
        queryBuilder.append("createdOn.gte:").append(formattedDate);

        return search(0, -1, null, null, queryBuilder.toString()).getElements();
    }

    public void refreshOpenTicketsAfterSlaOrAssigneeUpdate(Long categoryId, Long subCategoryId, Long issueTemplateId) throws InvalidSeachQueryException {
        Date twoMonthsAgo = Date.from(LocalDateTime.now().minusMonths(2).atZone(ZoneId.systemDefault()).toInstant());
        List<Long> ticketIds = getOpenTickets(categoryId, subCategoryId, issueTemplateId, twoMonthsAgo).stream().map(TicketEntry::getId).collect(Collectors.toList());
        AsyncService.submit(() -> {
            try {
                refreshTickets(ticketIds, true, true);
            } catch (BaseException e) {
                log.error("Error while auto-refreshing tickets for categoryId {} subCategoryId {} issueTemplateId id {}: {}", categoryId, subCategoryId, issueTemplateId, e.getMessage(), e);
                rollbarService.error(e);
            }
        });
    }
}
